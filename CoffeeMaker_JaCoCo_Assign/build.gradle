/*
 * This build file was generated by the Gradle 'init' task.
 *
 * This generated file contains a sample Java Library project to get you started.
 * For more details take a look at the Java Libraries chapter in the Gradle
 * user guide available at https://docs.gradle.org/3.5/userguide/java_library_plugin.html
 *
 * Extended by <PERSON> for Coursera "Introduction to Software Testing" course.
 * 8/2/2017  
 */

// Apply the java-library plugin to add support for Java Library
apply plugin: 'java-library'
apply plugin: 'jacoco'

// In this section you declare where to find the dependencies of your project
repositories {
    // Use jcenter for resolving your dependencies.
    // You can declare any Maven/Ivy/file repository here.
    jcenter()
    mavenCentral()
}

project.ext {
    junitVersion = '4.12'
}


dependencies {
    // This dependency is exported to consumers, that is to say found on their compile classpath.
    api 'org.apache.commons:commons-math3:3.6.1'

    // This dependency is used internally, and not exposed to consumers on their own compile classpath.
    implementation 'com.google.guava:guava:21.0'

    // Use JUnit test framework
    testImplementation 'junit:junit:4.12'
}

jaco<PERSON> {
    toolVersion = "0.7.6.************"
}

check.dependsOn jacocoTestReport
