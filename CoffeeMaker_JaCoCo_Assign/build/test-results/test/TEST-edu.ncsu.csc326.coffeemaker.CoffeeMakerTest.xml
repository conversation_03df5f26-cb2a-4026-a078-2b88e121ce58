<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" tests="94" skipped="0" failures="3" errors="0" timestamp="2025-08-11T13:55:35" hostname="pop-os" time="0.036">
  <properties/>
  <testcase name="testInventorySetSugarValid" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.005"/>
  <testcase name="testRecipeBookEditRecipeOutOfBounds" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.001"/>
  <testcase name="testRecipeSetAmtChocolateInvalidString" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testInventoryEnoughIngredientsFalseCoffee" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testRecipeEqualsNullCases" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testRecipeBookDeleteRecipeOutOfBounds" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testRecipeBookAddRecipeFull" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.004"/>
  <testcase name="testRecipeBookEditRecipeSuccess" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testInventorySetMilkNegative" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testCoffeeMakerMakeCoffeeInsufficientFunds" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testRecipeBookDeleteRecipeSuccess" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.001"/>
  <testcase name="testRecipeBookAddRecipeSuccess" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testRecipeSetAmtCoffeeValid" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testInventoryUseIngredientsWithBug" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testCoffeeMakerMakeCoffeeNullRecipe" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.001"/>
  <testcase name="testInventoryEnoughIngredientsExactBoundary" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testInventoryEnoughIngredientsTrue" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.001"/>
  <testcase name="testInventoryEnoughIngredientsFalseSugar" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testInventoryArithmeticOperations" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testInventorySetChocolateNegative" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testRecipeBookAddMultipleRecipes" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testCoffeeMakerMakeCoffeeExactPayment" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.001"/>
  <testcase name="testRecipeValidationBoundaries" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testInventoryAddChocolateInvalidString" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testCoffeeMakerIntegrationTest" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.006">
    <failure message="edu.ncsu.csc326.coffeemaker.exceptions.InventoryException: Units of sugar must be a positive integer" type="edu.ncsu.csc326.coffeemaker.exceptions.InventoryException">edu.ncsu.csc326.coffeemaker.exceptions.InventoryException: Units of sugar must be a positive integer
	at app//edu.ncsu.csc326.coffeemaker.Inventory.addSugar(Inventory.java:185)
	at app//edu.ncsu.csc326.coffeemaker.CoffeeMaker.addInventory(CoffeeMaker.java:67)
	at app//edu.ncsu.csc326.coffeemaker.CoffeeMakerTest.testCoffeeMakerIntegrationTest(CoffeeMakerTest.java:1380)
	at java.base@17.0.15/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base@17.0.15/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base@17.0.15/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base@17.0.15/java.lang.reflect.Method.invoke(Method.java:569)
	at app//junit.framework.TestCase.runTest(TestCase.java:176)
	at app//junit.framework.TestCase.runBare(TestCase.java:141)
	at app//junit.framework.TestResult$1.protect(TestResult.java:122)
	at app//junit.framework.TestResult.runProtected(TestResult.java:142)
	at app//junit.framework.TestResult.run(TestResult.java:125)
	at app//junit.framework.TestCase.run(TestCase.java:129)
	at app//junit.framework.TestSuite.runTest(TestSuite.java:252)
	at app//junit.framework.TestSuite.run(TestSuite.java:247)
	at app//org.junit.internal.runners.JUnit38ClassRunner.run(JUnit38ClassRunner.java:86)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:60)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:52)
	at java.base@17.0.15/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base@17.0.15/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base@17.0.15/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base@17.0.15/java.lang.reflect.Method.invoke(Method.java:569)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:94)
	at jdk.proxy2/jdk.proxy2.$Proxy5.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:176)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:129)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:100)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:60)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:113)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:65)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</failure>
  </testcase>
  <testcase name="testCoffeeMakerAddRecipe" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testRecipeBookAddRecipeDuplicate" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testInventoryAddMilkInvalidString" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testRecipeSetPriceInvalidString" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testCoffeeMakerMakeCoffeeInsufficientIngredients" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testRecipeSetPriceValid" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testCoffeeMakerAddInventoryInvalidSugar" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testInventorySetCoffeeNegative" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testRecipeEqualsCompleteEdgeCases" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testRecipeHashCode" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testInventoryUseIngredientsNotEnough" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testInventoryConstructor" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testCoffeeMakerAddInventoryInvalidCoffee" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testRecipeBookDeleteRecipeBugDetection" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testCoffeeMakerMakeCoffeeSuccess" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testCoffeeMakerConstructor" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testRecipeSetAmtSugarInvalidString" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.001"/>
  <testcase name="testCoffeeMakerEditRecipe" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testCoffeeMakerDeleteRecipeNull" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testInventoryToString" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testInventoryAddCoffeeInvalidNegative" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testCoffeeMakerGetRecipes" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testInventoryAddMilkValid" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testCoffeeMakerIntegrationWithExactValues" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testInventoryAddSugarInvalidString" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testInventoryAddChocolateValid" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testRecipeSetAmtMilkValid" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testRecipeSettersExactValidation" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testRecipeHashCodeAllBranches" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testInventoryUseIngredientsExactValues" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testCoffeeMakerAddInventoryValid" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0">
    <failure message="edu.ncsu.csc326.coffeemaker.exceptions.InventoryException: Units of sugar must be a positive integer" type="edu.ncsu.csc326.coffeemaker.exceptions.InventoryException">edu.ncsu.csc326.coffeemaker.exceptions.InventoryException: Units of sugar must be a positive integer
	at app//edu.ncsu.csc326.coffeemaker.Inventory.addSugar(Inventory.java:185)
	at app//edu.ncsu.csc326.coffeemaker.CoffeeMaker.addInventory(CoffeeMaker.java:67)
	at app//edu.ncsu.csc326.coffeemaker.CoffeeMakerTest.testCoffeeMakerAddInventoryValid(CoffeeMakerTest.java:1220)
	at java.base@17.0.15/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base@17.0.15/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base@17.0.15/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base@17.0.15/java.lang.reflect.Method.invoke(Method.java:569)
	at app//junit.framework.TestCase.runTest(TestCase.java:176)
	at app//junit.framework.TestCase.runBare(TestCase.java:141)
	at app//junit.framework.TestResult$1.protect(TestResult.java:122)
	at app//junit.framework.TestResult.runProtected(TestResult.java:142)
	at app//junit.framework.TestResult.run(TestResult.java:125)
	at app//junit.framework.TestCase.run(TestCase.java:129)
	at app//junit.framework.TestSuite.runTest(TestSuite.java:252)
	at app//junit.framework.TestSuite.run(TestSuite.java:247)
	at app//org.junit.internal.runners.JUnit38ClassRunner.run(JUnit38ClassRunner.java:86)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:60)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:52)
	at java.base@17.0.15/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base@17.0.15/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base@17.0.15/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base@17.0.15/java.lang.reflect.Method.invoke(Method.java:569)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:94)
	at jdk.proxy2/jdk.proxy2.$Proxy5.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:176)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:129)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:100)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:60)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:113)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:65)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</failure>
  </testcase>
  <testcase name="testRecipeSetName" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.001"/>
  <testcase name="testRecipeSetAmtMilkInvalidString" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testRecipeConstructor" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testRecipeSetPriceInvalidNegative" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testRecipeSetAmtChocolateValid" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testInventoryAddChocolateInvalidNegative" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testRecipeBookEditRecipeNull" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testInventoryAddCoffeeValid" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testRecipeBookConstructor" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testInventorySetChocolateValid" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testInventorySetSugarNegative" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testRecipeBookAddRecipeReturnValues" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testRecipeBookDeleteRecipeNegativeIndex" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testCoffeeMakerMakeCoffeeExactChange" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testRecipeSetAmtMilkInvalidNegative" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testInventoryEnoughIngredientsFalseMilk" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testInventoryAddSugarBugDetection" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testRecipeBookEditRecipeBugDetection" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testInventoryAddMilkInvalidNegative" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testRecipeEqualsEdgeCases" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.001"/>
  <testcase name="testRecipeSetAmtChocolateInvalidNegative" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testRecipeSetAmtCoffeeInvalidNegative" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testInventoryAddSugarValid" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testRecipeSetAmtCoffeeInvalidString" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testRecipeToString" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testInventorySetMilkValid" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testRecipeBookDeleteRecipeNull" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testInventoryEnoughIngredientsFalseChocolate" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testRecipeSetAmtSugarInvalidNegative" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testInventoryAddCoffeeInvalidString" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testCoffeeMakerAddRecipeDuplicate" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testCoffeeMakerAddInventoryInvalidChocolate" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.002">
    <failure message="junit.framework.ComparisonFailure: expected:&lt;Units of [chocolate] must be a positive ...&gt; but was:&lt;Units of [sugar] must be a positive ...&gt;" type="junit.framework.ComparisonFailure">junit.framework.ComparisonFailure: expected:&lt;Units of [chocolate] must be a positive ...&gt; but was:&lt;Units of [sugar] must be a positive ...&gt;
	at junit.framework.Assert.assertEquals(Assert.java:100)
	at junit.framework.Assert.assertEquals(Assert.java:107)
	at junit.framework.TestCase.assertEquals(TestCase.java:269)
	at edu.ncsu.csc326.coffeemaker.CoffeeMakerTest.testCoffeeMakerAddInventoryInvalidChocolate(CoffeeMakerTest.java:1263)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at junit.framework.TestCase.runTest(TestCase.java:176)
	at junit.framework.TestCase.runBare(TestCase.java:141)
	at junit.framework.TestResult$1.protect(TestResult.java:122)
	at junit.framework.TestResult.runProtected(TestResult.java:142)
	at junit.framework.TestResult.run(TestResult.java:125)
	at junit.framework.TestCase.run(TestCase.java:129)
	at junit.framework.TestSuite.runTest(TestSuite.java:252)
	at junit.framework.TestSuite.run(TestSuite.java:247)
	at org.junit.internal.runners.JUnit38ClassRunner.run(JUnit38ClassRunner.java:86)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:60)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:52)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:94)
	at jdk.proxy2/jdk.proxy2.$Proxy5.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:176)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:129)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:100)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:60)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:113)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:65)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</failure>
  </testcase>
  <testcase name="testCoffeeMakerAddInventoryInvalidMilk" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testRecipeSetAmtSugarValid" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testInventoryAddSugarBugWithZero" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testCoffeeMakerDeleteRecipe" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <testcase name="testRecipeEquals" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.001"/>
  <testcase name="testInventorySetCoffeeValid" classname="edu.ncsu.csc326.coffeemaker.CoffeeMakerTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
