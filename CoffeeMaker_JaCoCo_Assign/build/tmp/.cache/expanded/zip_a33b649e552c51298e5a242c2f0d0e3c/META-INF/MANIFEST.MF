Manifest-Version: 1.0
Archiver-Version: Plexus Archiver
Created-By: Apache Maven Bundle Plugin
Built-By: root
Build-Jdk: 1.8.0_322
Automatic-Module-Name: org.jacoco.agent
Bnd-LastModified: 1649143221327
Bundle-Description: JaCoCo Agent
Bundle-License: https://www.eclipse.org/legal/epl-2.0/
Bundle-ManifestVersion: 2
Bundle-Name: JaCoCo Agent
Bundle-RequiredExecutionEnvironment: J2SE-1.5
Bundle-SymbolicName: org.jacoco.agent
Bundle-Vendor: Mountainminds GmbH & Co. KG
Bundle-Version: 0.8.8.202204050719
Eclipse-SourceReferences: scm:git:git://github.com/jacoco/jacoco.git;p
 ath="org.jacoco.agent";commitId=5dcf34ad180c125ee3214437b0ca4f9b7b625
 8fc
Export-Package: org.jacoco.agent;version="0.8.8"
Require-Capability: osgi.ee;filter:="(&(osgi.ee=JavaSE)(version=1.5))"
Tool: Bnd-3.5.0.201709291849

