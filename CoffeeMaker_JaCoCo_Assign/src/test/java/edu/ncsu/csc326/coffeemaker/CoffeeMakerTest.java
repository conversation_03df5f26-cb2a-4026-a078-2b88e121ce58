package edu.ncsu.csc326.coffeemaker;

import junit.framework.TestCase;



/**
 * 
 * <AUTHOR>
 *
 * Extended by <PERSON>
 *
 * Unit tests for CoffeeMaker class.
 */

public class CoffeeMakerTest extends TestCase {
	
	private Recipe r1;
	private Recipe r2;
	private Recipe r3;
	private Recipe r4;
	private Recipe r5;
	private CoffeeMaker cm;
	private RecipeBook recipeBookStub;
	private Recipe [] stubRecipies; 
	
	protected void setUp() throws Exception {
		
		cm = new CoffeeMaker();
		
		//Set up for r1
		r1 = new Recipe();
		r1.setName("Coffee");
		r1.setAmtChocolate("0");
		r1.setAmtCoffee("3");
		r1.setAmtMilk("1");
		r1.setAmtSugar("1");
		r1.setPrice("50");
		
		//Set up for r2
		r2 = new Recipe();
		r2.setName("Mocha");
		r2.setAmtChocolate("20");
		r2.setAmtCoffee("3");
		r2.setAmtMilk("1");
		r2.setAmtSugar("1");
		r2.setPrice("75");
		
		//Set up for r3
		r3 = new Recipe();
		r3.setName("Latte");
		r3.setAmtChocolate("0");
		r3.setAmtCoffee("3");
		r3.setAmtMilk("3");
		r3.setAmtSugar("1");
		r3.setPrice("100");
		
		//Set up for r4
		r4 = new Recipe();
		r4.setName("Hot Chocolate");
		r4.setAmtChocolate("4");
		r4.setAmtCoffee("0");
		r4.setAmtMilk("1");
		r4.setAmtSugar("1");
		r4.setPrice("65");
		
		//Set up for r5 (added by MWW)
		r5 = new Recipe();
		r5.setName("Super Hot Chocolate");
		r5.setAmtChocolate("6");
		r5.setAmtCoffee("0");
		r5.setAmtMilk("1");
		r5.setAmtSugar("1");
		r5.setPrice("100");

		stubRecipies = new Recipe [] {r1, r2, r3};
		
		super.setUp();
	}
	
	
	// ========== Recipe Class Tests ==========

	public void testRecipeConstructor() {
		Recipe recipe = new Recipe();
		assertEquals("", recipe.getName());
		assertEquals(0, recipe.getPrice());
		assertEquals(0, recipe.getAmtCoffee());
		assertEquals(0, recipe.getAmtMilk());
		assertEquals(0, recipe.getAmtSugar());
		assertEquals(0, recipe.getAmtChocolate());
	}

	public void testRecipeSetName() {
		Recipe recipe = new Recipe();
		recipe.setName("Test Recipe");
		assertEquals("Test Recipe", recipe.getName());

		// Test null name - should not change
		recipe.setName(null);
		assertEquals("Test Recipe", recipe.getName());
	}

	public void testRecipeSetPriceValid() throws Exception {
		Recipe recipe = new Recipe();
		recipe.setPrice("50");
		assertEquals(50, recipe.getPrice());

		recipe.setPrice("0");
		assertEquals(0, recipe.getPrice());
	}

	public void testRecipeSetPriceInvalidNegative() {
		Recipe recipe = new Recipe();
		try {
			recipe.setPrice("-1");
			fail("Should throw RecipeException for negative price");
		} catch (Exception e) {
			assertEquals("Price must be a positive integer", e.getMessage());
		}
	}

	public void testRecipeSetPriceInvalidString() {
		Recipe recipe = new Recipe();
		try {
			recipe.setPrice("abc");
			fail("Should throw RecipeException for non-numeric price");
		} catch (Exception e) {
			assertEquals("Price must be a positive integer", e.getMessage());
		}
	}

	public void testRecipeSetAmtCoffeeValid() throws Exception {
		Recipe recipe = new Recipe();
		recipe.setAmtCoffee("3");
		assertEquals(3, recipe.getAmtCoffee());

		recipe.setAmtCoffee("0");
		assertEquals(0, recipe.getAmtCoffee());
	}

	public void testRecipeSetAmtCoffeeInvalidNegative() {
		Recipe recipe = new Recipe();
		try {
			recipe.setAmtCoffee("-1");
			fail("Should throw RecipeException for negative coffee amount");
		} catch (Exception e) {
			assertEquals("Units of coffee must be a positive integer", e.getMessage());
		}
	}

	public void testRecipeSetAmtCoffeeInvalidString() {
		Recipe recipe = new Recipe();
		try {
			recipe.setAmtCoffee("abc");
			fail("Should throw RecipeException for non-numeric coffee amount");
		} catch (Exception e) {
			assertEquals("Units of coffee must be a positive integer", e.getMessage());
		}
	}

	public void testRecipeSetAmtMilkValid() throws Exception {
		Recipe recipe = new Recipe();
		recipe.setAmtMilk("2");
		assertEquals(2, recipe.getAmtMilk());

		recipe.setAmtMilk("0");
		assertEquals(0, recipe.getAmtMilk());
	}

	public void testRecipeSetAmtMilkInvalidNegative() {
		Recipe recipe = new Recipe();
		try {
			recipe.setAmtMilk("-1");
			fail("Should throw RecipeException for negative milk amount");
		} catch (Exception e) {
			assertEquals("Units of milk must be a positive integer", e.getMessage());
		}
	}

	public void testRecipeSetAmtMilkInvalidString() {
		Recipe recipe = new Recipe();
		try {
			recipe.setAmtMilk("xyz");
			fail("Should throw RecipeException for non-numeric milk amount");
		} catch (Exception e) {
			assertEquals("Units of milk must be a positive integer", e.getMessage());
		}
	}

	public void testRecipeSetAmtSugarValid() throws Exception {
		Recipe recipe = new Recipe();
		recipe.setAmtSugar("1");
		assertEquals(1, recipe.getAmtSugar());

		recipe.setAmtSugar("0");
		assertEquals(0, recipe.getAmtSugar());
	}

	public void testRecipeSetAmtSugarInvalidNegative() {
		Recipe recipe = new Recipe();
		try {
			recipe.setAmtSugar("-1");
			fail("Should throw RecipeException for negative sugar amount");
		} catch (Exception e) {
			assertEquals("Units of sugar must be a positive integer", e.getMessage());
		}
	}

	public void testRecipeSetAmtSugarInvalidString() {
		Recipe recipe = new Recipe();
		try {
			recipe.setAmtSugar("def");
			fail("Should throw RecipeException for non-numeric sugar amount");
		} catch (Exception e) {
			assertEquals("Units of sugar must be a positive integer", e.getMessage());
		}
	}

	public void testRecipeSetAmtChocolateValid() throws Exception {
		Recipe recipe = new Recipe();
		recipe.setAmtChocolate("4");
		assertEquals(4, recipe.getAmtChocolate());

		recipe.setAmtChocolate("0");
		assertEquals(0, recipe.getAmtChocolate());
	}

	public void testRecipeSetAmtChocolateInvalidNegative() {
		Recipe recipe = new Recipe();
		try {
			recipe.setAmtChocolate("-1");
			fail("Should throw RecipeException for negative chocolate amount");
		} catch (Exception e) {
			assertEquals("Units of chocolate must be a positive integer", e.getMessage());
		}
	}

	public void testRecipeSetAmtChocolateInvalidString() {
		Recipe recipe = new Recipe();
		try {
			recipe.setAmtChocolate("ghi");
			fail("Should throw RecipeException for non-numeric chocolate amount");
		} catch (Exception e) {
			assertEquals("Units of chocolate must be a positive integer", e.getMessage());
		}
	}

	public void testRecipeToString() throws Exception {
		Recipe recipe = new Recipe();
		recipe.setName("Test Recipe");
		assertEquals("Test Recipe", recipe.toString());

		// Test empty name
		Recipe emptyRecipe = new Recipe();
		assertEquals("", emptyRecipe.toString());
	}

	public void testRecipeEquals() throws Exception {
		Recipe recipe1 = new Recipe();
		recipe1.setName("Coffee");

		Recipe recipe2 = new Recipe();
		recipe2.setName("Coffee");

		Recipe recipe3 = new Recipe();
		recipe3.setName("Tea");

		// Test equality based on name
		assertTrue(recipe1.equals(recipe2));
		assertFalse(recipe1.equals(recipe3));

		// Test reflexivity
		assertTrue(recipe1.equals(recipe1));

		// Test null
		assertFalse(recipe1.equals(null));

		// Test different class
		assertFalse(recipe1.equals("Coffee"));

		// Test null name
		Recipe nullNameRecipe1 = new Recipe();
		Recipe nullNameRecipe2 = new Recipe();
		assertTrue(nullNameRecipe1.equals(nullNameRecipe2));

		assertFalse(nullNameRecipe1.equals(recipe1));
		assertFalse(recipe1.equals(nullNameRecipe1));
	}

	public void testRecipeHashCode() throws Exception {
		Recipe recipe1 = new Recipe();
		recipe1.setName("Coffee");

		Recipe recipe2 = new Recipe();
		recipe2.setName("Coffee");

		Recipe recipe3 = new Recipe();
		recipe3.setName("Tea");

		// Equal objects should have equal hash codes
		assertEquals(recipe1.hashCode(), recipe2.hashCode());

		// Different objects may have different hash codes
		assertFalse(recipe1.hashCode() == recipe3.hashCode());

		// Test null name
		Recipe nullNameRecipe = new Recipe();
		assertEquals(31, nullNameRecipe.hashCode()); // 31 * 1 + 0
	}

	public void testRecipeEqualsNullCases() throws Exception {
		Recipe recipe1 = new Recipe();
		recipe1.setName("Coffee");

		Recipe recipe2 = new Recipe();
		recipe2.setName("Coffee");

		// Test with null name on first recipe
		Recipe nullNameRecipe = new Recipe();
		Recipe normalRecipe = new Recipe();
		normalRecipe.setName("Test");

		assertFalse(nullNameRecipe.equals(normalRecipe));
		assertFalse(normalRecipe.equals(nullNameRecipe));

		// Test hashCode with non-null name
		Recipe namedRecipe = new Recipe();
		namedRecipe.setName("TestRecipe");
		int expectedHash = 31 * 1 + "TestRecipe".hashCode();
		assertEquals(expectedHash, namedRecipe.hashCode());
	}

	public void testRecipeEqualsEdgeCases() throws Exception {
		// Test the specific missing branches in equals method
		Recipe recipe1 = new Recipe();
		recipe1.setName("Coffee");

		Recipe recipe2 = new Recipe();
		recipe2.setName("Tea");

		// Test when both names are not null but different
		assertFalse(recipe1.equals(recipe2));

		// Test when first recipe has null name, second has non-null
		Recipe nullNameRecipe = new Recipe();
		assertFalse(nullNameRecipe.equals(recipe1));

		// Test when first recipe has non-null name, second has null
		assertFalse(recipe1.equals(nullNameRecipe));

		// Test hashCode with null name (covers the missing branch)
		Recipe nullRecipe = new Recipe();
		assertEquals(31, nullRecipe.hashCode());

		// Test hashCode with non-null name (covers the other branch)
		Recipe namedRecipe2 = new Recipe();
		namedRecipe2.setName("Latte");
		int expected = 31 * 1 + "Latte".hashCode();
		assertEquals(expected, namedRecipe2.hashCode());
	}

	public void testRecipeEqualsCompleteEdgeCases() throws Exception {
		Recipe recipe1 = new Recipe();
		Recipe recipe2 = new Recipe();

		// Test reflexivity (this == obj) - line 171
		assertTrue(recipe1.equals(recipe1));

		// Test null comparison - line 173
		assertFalse(recipe1.equals(null));

		// Test different class comparison - line 175
		assertFalse(recipe1.equals("not a recipe"));

		// Test when this.name is null and other.name is null - should be equal
		assertTrue(recipe1.equals(recipe2)); // Both have null/empty names

		// Test when this.name is null and other.name is not null
		recipe2.setName("Coffee");
		assertFalse(recipe1.equals(recipe2));

		// Test when this.name is not null and other.name is null
		recipe1.setName("Tea");
		recipe2.setName("");  // Reset to empty
		Recipe emptyNameRecipe = new Recipe(); // This will have empty string name
		assertFalse(recipe1.equals(emptyNameRecipe));

		// Test when both names are not null but different
		recipe1.setName("Coffee");
		recipe2.setName("Tea");
		assertFalse(recipe1.equals(recipe2));

		// Test when both names are not null and equal
		recipe2.setName("Coffee");
		assertTrue(recipe1.equals(recipe2));
	}

	public void testRecipeHashCodeAllBranches() {
		// Test hashCode with empty string name (default constructor)
		Recipe emptyNameRecipe = new Recipe();
		int expectedEmpty = 31 * 1 + "".hashCode();
		assertEquals(expectedEmpty, emptyNameRecipe.hashCode());

		// Test hashCode with null name (if we can set it to null somehow)
		Recipe nullNameRecipe = new Recipe();
		nullNameRecipe.setName(null); // This should not change the name due to null check
		// Name should still be empty string, so same hash as above
		assertEquals(expectedEmpty, nullNameRecipe.hashCode());

		// Test hashCode with actual name
		Recipe namedRecipe = new Recipe();
		namedRecipe.setName("TestName");
		int expectedNamed = 31 * 1 + "TestName".hashCode();
		assertEquals(expectedNamed, namedRecipe.hashCode());
	}

	// ========== Inventory Class Tests ==========

	public void testInventoryConstructor() {
		Inventory inventory = new Inventory();
		assertEquals(15, inventory.getCoffee());
		assertEquals(15, inventory.getMilk());
		assertEquals(15, inventory.getSugar());
		assertEquals(15, inventory.getChocolate());
	}

	public void testInventorySetCoffeeValid() {
		Inventory inventory = new Inventory();
		inventory.setCoffee(10);
		assertEquals(10, inventory.getCoffee());

		inventory.setCoffee(0);
		assertEquals(0, inventory.getCoffee());
	}

	public void testInventorySetCoffeeNegative() {
		Inventory inventory = new Inventory();
		inventory.setCoffee(-5);
		// Should not change from initial value when negative
		assertEquals(15, inventory.getCoffee());
	}

	public void testInventorySetMilkValid() {
		Inventory inventory = new Inventory();
		inventory.setMilk(8);
		assertEquals(8, inventory.getMilk());

		inventory.setMilk(0);
		assertEquals(0, inventory.getMilk());
	}

	public void testInventorySetMilkNegative() {
		Inventory inventory = new Inventory();
		inventory.setMilk(-3);
		// Should not change from initial value when negative
		assertEquals(15, inventory.getMilk());
	}

	public void testInventorySetSugarValid() {
		Inventory inventory = new Inventory();
		inventory.setSugar(12);
		assertEquals(12, inventory.getSugar());

		inventory.setSugar(0);
		assertEquals(0, inventory.getSugar());
	}

	public void testInventorySetSugarNegative() {
		Inventory inventory = new Inventory();
		inventory.setSugar(-2);
		// Should not change from initial value when negative
		assertEquals(15, inventory.getSugar());
	}

	public void testInventorySetChocolateValid() {
		Inventory inventory = new Inventory();
		inventory.setChocolate(6);
		assertEquals(6, inventory.getChocolate());

		inventory.setChocolate(0);
		assertEquals(0, inventory.getChocolate());
	}

	public void testInventorySetChocolateNegative() {
		Inventory inventory = new Inventory();
		inventory.setChocolate(-4);
		// Should not change from initial value when negative
		assertEquals(15, inventory.getChocolate());
	}

	public void testInventoryAddCoffeeValid() throws Exception {
		Inventory inventory = new Inventory();
		inventory.addCoffee("5");
		assertEquals(20, inventory.getCoffee()); // 15 + 5

		inventory.addCoffee("0");
		assertEquals(20, inventory.getCoffee()); // 20 + 0
	}

	public void testInventoryAddCoffeeInvalidNegative() {
		Inventory inventory = new Inventory();
		try {
			inventory.addCoffee("-1");
			fail("Should throw InventoryException for negative coffee amount");
		} catch (Exception e) {
			assertEquals("Units of coffee must be a positive integer", e.getMessage());
		}
	}

	public void testInventoryAddCoffeeInvalidString() {
		Inventory inventory = new Inventory();
		try {
			inventory.addCoffee("abc");
			fail("Should throw InventoryException for non-numeric coffee amount");
		} catch (Exception e) {
			assertEquals("Units of coffee must be a positive integer", e.getMessage());
		}
	}

	public void testInventoryAddMilkValid() throws Exception {
		Inventory inventory = new Inventory();
		inventory.addMilk("3");
		assertEquals(18, inventory.getMilk()); // 15 + 3

		inventory.addMilk("0");
		assertEquals(18, inventory.getMilk()); // 18 + 0
	}

	public void testInventoryAddMilkInvalidNegative() {
		Inventory inventory = new Inventory();
		try {
			inventory.addMilk("-2");
			fail("Should throw InventoryException for negative milk amount");
		} catch (Exception e) {
			assertEquals("Units of milk must be a positive integer", e.getMessage());
		}
	}

	public void testInventoryAddMilkInvalidString() {
		Inventory inventory = new Inventory();
		try {
			inventory.addMilk("xyz");
			fail("Should throw InventoryException for non-numeric milk amount");
		} catch (Exception e) {
			assertEquals("Units of milk must be a positive integer", e.getMessage());
		}
	}

	// This test will reveal the bug in addSugar method
	public void testInventoryAddSugarValid() throws Exception {
		Inventory inventory = new Inventory();
		try {
			inventory.addSugar("2");
			// Due to the bug, this should fail because positive numbers throw exception
			fail("Expected InventoryException due to bug in addSugar method");
		} catch (Exception e) {
			assertEquals("Units of sugar must be a positive integer", e.getMessage());
		}
	}

	// This test shows the inverted logic in addSugar
	public void testInventoryAddSugarBugWithZero() throws Exception {
		Inventory inventory = new Inventory();
		inventory.addSugar("0");
		assertEquals(15, inventory.getSugar()); // Should remain 15 (15 + 0)
	}

	public void testInventoryAddSugarInvalidString() {
		Inventory inventory = new Inventory();
		try {
			inventory.addSugar("def");
			fail("Should throw InventoryException for non-numeric sugar amount");
		} catch (Exception e) {
			assertEquals("Units of sugar must be a positive integer", e.getMessage());
		}
	}

	public void testInventoryAddChocolateValid() throws Exception {
		Inventory inventory = new Inventory();
		inventory.addChocolate("4");
		assertEquals(19, inventory.getChocolate()); // 15 + 4

		inventory.addChocolate("0");
		assertEquals(19, inventory.getChocolate()); // 19 + 0
	}

	public void testInventoryAddChocolateInvalidNegative() {
		Inventory inventory = new Inventory();
		try {
			inventory.addChocolate("-3");
			fail("Should throw InventoryException for negative chocolate amount");
		} catch (Exception e) {
			assertEquals("Units of chocolate must be a positive integer", e.getMessage());
		}
	}

	public void testInventoryAddChocolateInvalidString() {
		Inventory inventory = new Inventory();
		try {
			inventory.addChocolate("ghi");
			fail("Should throw InventoryException for non-numeric chocolate amount");
		} catch (Exception e) {
			assertEquals("Units of chocolate must be a positive integer", e.getMessage());
		}
	}

	public void testInventoryEnoughIngredientsTrue() throws Exception {
		Inventory inventory = new Inventory();
		// Initial inventory has 15 of each ingredient

		Recipe recipe = new Recipe();
		recipe.setAmtCoffee("10");
		recipe.setAmtMilk("5");
		recipe.setAmtSugar("3");
		recipe.setAmtChocolate("8");

		assertTrue(inventory.enoughIngredients(recipe));
	}

	public void testInventoryEnoughIngredientsFalseCoffee() throws Exception {
		Inventory inventory = new Inventory();
		inventory.setCoffee(2); // Set coffee to 2

		Recipe recipe = new Recipe();
		recipe.setAmtCoffee("5"); // Need 5 coffee
		recipe.setAmtMilk("1");
		recipe.setAmtSugar("1");
		recipe.setAmtChocolate("1");

		assertFalse(inventory.enoughIngredients(recipe));
	}

	public void testInventoryEnoughIngredientsFalseMilk() throws Exception {
		Inventory inventory = new Inventory();
		inventory.setMilk(1); // Set milk to 1

		Recipe recipe = new Recipe();
		recipe.setAmtCoffee("1");
		recipe.setAmtMilk("5"); // Need 5 milk
		recipe.setAmtSugar("1");
		recipe.setAmtChocolate("1");

		assertFalse(inventory.enoughIngredients(recipe));
	}

	public void testInventoryEnoughIngredientsFalseSugar() throws Exception {
		Inventory inventory = new Inventory();
		inventory.setSugar(1); // Set sugar to 1

		Recipe recipe = new Recipe();
		recipe.setAmtCoffee("1");
		recipe.setAmtMilk("1");
		recipe.setAmtSugar("5"); // Need 5 sugar
		recipe.setAmtChocolate("1");

		assertFalse(inventory.enoughIngredients(recipe));
	}

	public void testInventoryEnoughIngredientsFalseChocolate() throws Exception {
		Inventory inventory = new Inventory();
		inventory.setChocolate(1); // Set chocolate to 1

		Recipe recipe = new Recipe();
		recipe.setAmtCoffee("1");
		recipe.setAmtMilk("1");
		recipe.setAmtSugar("1");
		recipe.setAmtChocolate("5"); // Need 5 chocolate

		assertFalse(inventory.enoughIngredients(recipe));
	}

	// This test will reveal the bug in useIngredients method
	public void testInventoryUseIngredientsWithBug() throws Exception {
		Inventory inventory = new Inventory();
		// Initial: coffee=15, milk=15, sugar=15, chocolate=15

		Recipe recipe = new Recipe();
		recipe.setAmtCoffee("3");
		recipe.setAmtMilk("2");
		recipe.setAmtSugar("1");
		recipe.setAmtChocolate("4");

		assertTrue(inventory.useIngredients(recipe));

		// Due to the bug, coffee is added instead of subtracted
		assertEquals(18, inventory.getCoffee()); // Should be 12, but bug makes it 18
		assertEquals(13, inventory.getMilk());   // 15 - 2 = 13 (correct)
		assertEquals(14, inventory.getSugar());  // 15 - 1 = 14 (correct)
		assertEquals(11, inventory.getChocolate()); // 15 - 4 = 11 (correct)
	}

	public void testInventoryUseIngredientsNotEnough() throws Exception {
		Inventory inventory = new Inventory();
		inventory.setCoffee(1); // Set coffee to 1

		Recipe recipe = new Recipe();
		recipe.setAmtCoffee("5"); // Need 5 coffee, but only have 1
		recipe.setAmtMilk("1");
		recipe.setAmtSugar("1");
		recipe.setAmtChocolate("1");

		assertFalse(inventory.useIngredients(recipe));

		// Inventory should remain unchanged
		assertEquals(1, inventory.getCoffee());
		assertEquals(15, inventory.getMilk());
		assertEquals(15, inventory.getSugar());
		assertEquals(15, inventory.getChocolate());
	}

	public void testInventoryToString() {
		Inventory inventory = new Inventory();
		String expected = "Coffee: 15\nMilk: 15\nSugar: 15\nChocolate: 15\n";
		assertEquals(expected, inventory.toString());

		// Test with different values
		inventory.setCoffee(10);
		inventory.setMilk(8);
		inventory.setSugar(6);
		inventory.setChocolate(4);

		String expected2 = "Coffee: 10\nMilk: 8\nSugar: 6\nChocolate: 4\n";
		assertEquals(expected2, inventory.toString());
	}

	// ========== RecipeBook Class Tests ==========

	public void testRecipeBookConstructor() {
		RecipeBook recipeBook = new RecipeBook();
		Recipe[] recipes = recipeBook.getRecipes();
		assertEquals(4, recipes.length);
		for (int i = 0; i < recipes.length; i++) {
			assertNull(recipes[i]);
		}
	}

	public void testRecipeBookAddRecipeSuccess() throws Exception {
		RecipeBook recipeBook = new RecipeBook();
		Recipe recipe = new Recipe();
		recipe.setName("Coffee");

		assertTrue(recipeBook.addRecipe(recipe));

		Recipe[] recipes = recipeBook.getRecipes();
		assertEquals(recipe, recipes[0]);
		assertNull(recipes[1]);
		assertNull(recipes[2]);
		assertNull(recipes[3]);
	}

	public void testRecipeBookAddRecipeDuplicate() throws Exception {
		RecipeBook recipeBook = new RecipeBook();
		Recipe recipe1 = new Recipe();
		recipe1.setName("Coffee");

		Recipe recipe2 = new Recipe();
		recipe2.setName("Coffee"); // Same name

		assertTrue(recipeBook.addRecipe(recipe1));
		assertFalse(recipeBook.addRecipe(recipe2)); // Should fail - duplicate

		Recipe[] recipes = recipeBook.getRecipes();
		assertEquals(recipe1, recipes[0]);
		assertNull(recipes[1]);
	}

	public void testRecipeBookAddRecipeFull() throws Exception {
		RecipeBook recipeBook = new RecipeBook();

		// Fill all 4 slots
		for (int i = 0; i < 4; i++) {
			Recipe recipe = new Recipe();
			recipe.setName("Recipe" + i);
			assertTrue(recipeBook.addRecipe(recipe));
		}

		// Try to add 5th recipe - should fail
		Recipe extraRecipe = new Recipe();
		extraRecipe.setName("Extra Recipe");
		assertFalse(recipeBook.addRecipe(extraRecipe));
	}

	public void testRecipeBookAddMultipleRecipes() throws Exception {
		RecipeBook recipeBook = new RecipeBook();

		Recipe recipe1 = new Recipe();
		recipe1.setName("Coffee");
		Recipe recipe2 = new Recipe();
		recipe2.setName("Tea");
		Recipe recipe3 = new Recipe();
		recipe3.setName("Latte");

		assertTrue(recipeBook.addRecipe(recipe1));
		assertTrue(recipeBook.addRecipe(recipe2));
		assertTrue(recipeBook.addRecipe(recipe3));

		Recipe[] recipes = recipeBook.getRecipes();
		assertEquals(recipe1, recipes[0]);
		assertEquals(recipe2, recipes[1]);
		assertEquals(recipe3, recipes[2]);
		assertNull(recipes[3]);
	}

	public void testRecipeBookDeleteRecipeSuccess() throws Exception {
		RecipeBook recipeBook = new RecipeBook();
		Recipe recipe = new Recipe();
		recipe.setName("Coffee");

		recipeBook.addRecipe(recipe);
		String deletedName = recipeBook.deleteRecipe(0);

		assertEquals("Coffee", deletedName);

		// Due to the bug, it creates a new Recipe() instead of setting to null
		Recipe[] recipes = recipeBook.getRecipes();
		assertNotNull(recipes[0]); // Bug: should be null
		assertEquals("", recipes[0].getName()); // Bug: creates empty recipe
	}

	public void testRecipeBookDeleteRecipeNull() {
		RecipeBook recipeBook = new RecipeBook();
		String deletedName = recipeBook.deleteRecipe(0);
		assertNull(deletedName);
	}

	public void testRecipeBookDeleteRecipeOutOfBounds() {
		RecipeBook recipeBook = new RecipeBook();
		try {
			recipeBook.deleteRecipe(5); // Index out of bounds
			fail("Should throw ArrayIndexOutOfBoundsException");
		} catch (ArrayIndexOutOfBoundsException e) {
			// Expected
		}
	}

	public void testRecipeBookDeleteRecipeNegativeIndex() {
		RecipeBook recipeBook = new RecipeBook();
		try {
			recipeBook.deleteRecipe(-1); // Negative index
			fail("Should throw ArrayIndexOutOfBoundsException");
		} catch (ArrayIndexOutOfBoundsException e) {
			// Expected
		}
	}

	public void testRecipeBookEditRecipeSuccess() throws Exception {
		RecipeBook recipeBook = new RecipeBook();
		Recipe originalRecipe = new Recipe();
		originalRecipe.setName("Coffee");
		originalRecipe.setPrice("50");

		recipeBook.addRecipe(originalRecipe);

		Recipe newRecipe = new Recipe();
		newRecipe.setName("New Coffee");
		newRecipe.setPrice("75");

		String editedName = recipeBook.editRecipe(0, newRecipe);

		assertEquals("Coffee", editedName); // Returns original name

		Recipe[] recipes = recipeBook.getRecipes();
		assertEquals(newRecipe, recipes[0]);
		// Due to the bug, the new recipe's name is set to empty string
		assertEquals("", recipes[0].getName()); // Bug: name is cleared
	}

	public void testRecipeBookEditRecipeNull() throws Exception {
		RecipeBook recipeBook = new RecipeBook();
		Recipe newRecipe = new Recipe();
		newRecipe.setName("New Recipe");

		String editedName = recipeBook.editRecipe(0, newRecipe);
		assertNull(editedName);
	}

	public void testRecipeBookEditRecipeOutOfBounds() throws Exception {
		RecipeBook recipeBook = new RecipeBook();
		Recipe newRecipe = new Recipe();
		newRecipe.setName("New Recipe");

		try {
			recipeBook.editRecipe(5, newRecipe); // Index out of bounds
			fail("Should throw ArrayIndexOutOfBoundsException");
		} catch (ArrayIndexOutOfBoundsException e) {
			// Expected
		}
	}

	// ========== CoffeeMaker Class Tests ==========

	public void testCoffeeMakerConstructor() {
		CoffeeMaker coffeeMaker = new CoffeeMaker();
		Recipe[] recipes = coffeeMaker.getRecipes();
		assertEquals(4, recipes.length);
		for (int i = 0; i < recipes.length; i++) {
			assertNull(recipes[i]);
		}

		// Check initial inventory
		String inventory = coffeeMaker.checkInventory();
		String expected = "Coffee: 15\nMilk: 15\nSugar: 15\nChocolate: 15\n";
		assertEquals(expected, inventory);
	}

	public void testCoffeeMakerAddRecipe() throws Exception {
		CoffeeMaker coffeeMaker = new CoffeeMaker();
		Recipe recipe = new Recipe();
		recipe.setName("Coffee");
		recipe.setPrice("50");
		recipe.setAmtCoffee("3");
		recipe.setAmtMilk("1");
		recipe.setAmtSugar("1");
		recipe.setAmtChocolate("0");

		assertTrue(coffeeMaker.addRecipe(recipe));

		Recipe[] recipes = coffeeMaker.getRecipes();
		assertEquals(recipe, recipes[0]);
	}

	public void testCoffeeMakerAddRecipeDuplicate() throws Exception {
		CoffeeMaker coffeeMaker = new CoffeeMaker();
		Recipe recipe1 = new Recipe();
		recipe1.setName("Coffee");

		Recipe recipe2 = new Recipe();
		recipe2.setName("Coffee"); // Same name

		assertTrue(coffeeMaker.addRecipe(recipe1));
		assertFalse(coffeeMaker.addRecipe(recipe2)); // Should fail
	}

	public void testCoffeeMakerDeleteRecipe() throws Exception {
		CoffeeMaker coffeeMaker = new CoffeeMaker();
		Recipe recipe = new Recipe();
		recipe.setName("Coffee");

		coffeeMaker.addRecipe(recipe);
		String deletedName = coffeeMaker.deleteRecipe(0);

		assertEquals("Coffee", deletedName);
	}

	public void testCoffeeMakerDeleteRecipeNull() {
		CoffeeMaker coffeeMaker = new CoffeeMaker();
		String deletedName = coffeeMaker.deleteRecipe(0);
		assertNull(deletedName);
	}

	public void testCoffeeMakerEditRecipe() throws Exception {
		CoffeeMaker coffeeMaker = new CoffeeMaker();
		Recipe originalRecipe = new Recipe();
		originalRecipe.setName("Coffee");

		coffeeMaker.addRecipe(originalRecipe);

		Recipe newRecipe = new Recipe();
		newRecipe.setName("New Coffee");

		String editedName = coffeeMaker.editRecipe(0, newRecipe);
		assertEquals("Coffee", editedName);
	}

	public void testCoffeeMakerAddInventoryValid() throws Exception {
		CoffeeMaker coffeeMaker = new CoffeeMaker();
		coffeeMaker.addInventory("5", "3", "2", "4");

		String inventory = coffeeMaker.checkInventory();
		String expected = "Coffee: 20\nMilk: 18\nSugar: 17\nChocolate: 19\n";
		assertEquals(expected, inventory);
	}

	public void testCoffeeMakerAddInventoryInvalidCoffee() {
		CoffeeMaker coffeeMaker = new CoffeeMaker();
		try {
			coffeeMaker.addInventory("abc", "3", "2", "4");
			fail("Should throw InventoryException for invalid coffee amount");
		} catch (Exception e) {
			assertEquals("Units of coffee must be a positive integer", e.getMessage());
		}
	}

	public void testCoffeeMakerAddInventoryInvalidMilk() {
		CoffeeMaker coffeeMaker = new CoffeeMaker();
		try {
			coffeeMaker.addInventory("5", "xyz", "2", "4");
			fail("Should throw InventoryException for invalid milk amount");
		} catch (Exception e) {
			assertEquals("Units of milk must be a positive integer", e.getMessage());
		}
	}

	public void testCoffeeMakerAddInventoryInvalidSugar() {
		CoffeeMaker coffeeMaker = new CoffeeMaker();
		try {
			coffeeMaker.addInventory("5", "3", "def", "4");
			fail("Should throw InventoryException for invalid sugar amount");
		} catch (Exception e) {
			assertEquals("Units of sugar must be a positive integer", e.getMessage());
		}
	}

	public void testCoffeeMakerAddInventoryInvalidChocolate() {
		CoffeeMaker coffeeMaker = new CoffeeMaker();
		try {
			coffeeMaker.addInventory("5", "3", "2", "ghi");
			fail("Should throw InventoryException for invalid chocolate amount");
		} catch (Exception e) {
			assertEquals("Units of chocolate must be a positive integer", e.getMessage());
		}
	}

	public void testCoffeeMakerMakeCoffeeSuccess() throws Exception {
		CoffeeMaker coffeeMaker = new CoffeeMaker();
		Recipe recipe = new Recipe();
		recipe.setName("Coffee");
		recipe.setPrice("50");
		recipe.setAmtCoffee("3");
		recipe.setAmtMilk("1");
		recipe.setAmtSugar("1");
		recipe.setAmtChocolate("0");

		coffeeMaker.addRecipe(recipe);

		int change = coffeeMaker.makeCoffee(0, 75);
		assertEquals(25, change); // 75 - 50 = 25
	}

	public void testCoffeeMakerMakeCoffeeInsufficientFunds() throws Exception {
		CoffeeMaker coffeeMaker = new CoffeeMaker();
		Recipe recipe = new Recipe();
		recipe.setName("Coffee");
		recipe.setPrice("50");
		recipe.setAmtCoffee("3");
		recipe.setAmtMilk("1");
		recipe.setAmtSugar("1");
		recipe.setAmtChocolate("0");

		coffeeMaker.addRecipe(recipe);

		int change = coffeeMaker.makeCoffee(0, 25); // Not enough money
		assertEquals(25, change); // Returns all money back
	}

	public void testCoffeeMakerMakeCoffeeNullRecipe() {
		CoffeeMaker coffeeMaker = new CoffeeMaker();
		int change = coffeeMaker.makeCoffee(0, 50); // No recipe at index 0
		assertEquals(50, change); // Returns all money back
	}

	public void testCoffeeMakerMakeCoffeeInsufficientIngredients() throws Exception {
		CoffeeMaker coffeeMaker = new CoffeeMaker();
		Recipe recipe = new Recipe();
		recipe.setName("Expensive Coffee");
		recipe.setPrice("50");
		recipe.setAmtCoffee("20"); // Need 20 coffee, but only have 15
		recipe.setAmtMilk("1");
		recipe.setAmtSugar("1");
		recipe.setAmtChocolate("0");

		coffeeMaker.addRecipe(recipe);

		int change = coffeeMaker.makeCoffee(0, 75);
		assertEquals(75, change); // Returns all money back due to insufficient ingredients
	}

	public void testCoffeeMakerMakeCoffeeExactPayment() throws Exception {
		CoffeeMaker coffeeMaker = new CoffeeMaker();
		Recipe recipe = new Recipe();
		recipe.setName("Coffee");
		recipe.setPrice("50");
		recipe.setAmtCoffee("3");
		recipe.setAmtMilk("1");
		recipe.setAmtSugar("1");
		recipe.setAmtChocolate("0");

		coffeeMaker.addRecipe(recipe);

		int change = coffeeMaker.makeCoffee(0, 50); // Exact payment
		assertEquals(0, change); // No change
	}

	public void testCoffeeMakerGetRecipes() throws Exception {
		CoffeeMaker coffeeMaker = new CoffeeMaker();
		Recipe recipe1 = new Recipe();
		recipe1.setName("Coffee");
		Recipe recipe2 = new Recipe();
		recipe2.setName("Tea");

		coffeeMaker.addRecipe(recipe1);
		coffeeMaker.addRecipe(recipe2);

		Recipe[] recipes = coffeeMaker.getRecipes();
		assertEquals(4, recipes.length);
		assertEquals(recipe1, recipes[0]);
		assertEquals(recipe2, recipes[1]);
		assertNull(recipes[2]);
		assertNull(recipes[3]);
	}

	// Integration test combining multiple operations
	public void testCoffeeMakerIntegrationTest() throws Exception {
		CoffeeMaker coffeeMaker = new CoffeeMaker();

		// Add recipes
		Recipe coffee = new Recipe();
		coffee.setName("Coffee");
		coffee.setPrice("50");
		coffee.setAmtCoffee("3");
		coffee.setAmtMilk("1");
		coffee.setAmtSugar("1");
		coffee.setAmtChocolate("0");

		Recipe mocha = new Recipe();
		mocha.setName("Mocha");
		mocha.setPrice("75");
		mocha.setAmtCoffee("3");
		mocha.setAmtMilk("1");
		mocha.setAmtSugar("1");
		mocha.setAmtChocolate("3");

		assertTrue(coffeeMaker.addRecipe(coffee));
		assertTrue(coffeeMaker.addRecipe(mocha));

		// Add inventory
		coffeeMaker.addInventory("10", "10", "10", "10");

		// Make coffee
		int change1 = coffeeMaker.makeCoffee(0, 60); // Coffee costs 50
		assertEquals(10, change1);

		// Make mocha
		int change2 = coffeeMaker.makeCoffee(1, 100); // Mocha costs 75
		assertEquals(25, change2);

		// Try to make another coffee - should work due to bug in useIngredients
		// (coffee gets added instead of subtracted)
		int change3 = coffeeMaker.makeCoffee(0, 60);
		assertEquals(10, change3);
	}


}
